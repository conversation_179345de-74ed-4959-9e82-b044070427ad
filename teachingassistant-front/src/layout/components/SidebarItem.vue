<template>
  <template v-if="!route.meta?.hidden">
    <!-- 有子菜单的情况 -->
    <el-sub-menu
      v-if="hasChildren"
      :index="route.path"
      :popper-append-to-body="false"
    >
      <template #title>
        <el-icon v-if="route.meta?.icon">
          <component :is="route.meta.icon" />
        </el-icon>
        <span>{{ route.meta?.title }}</span>
      </template>
      
      <template v-for="child in visibleChildren" :key="child.path">
        <el-menu-item :index="getChildPath(child)">
          <el-icon v-if="child.meta?.icon">
            <component :is="child.meta.icon" />
          </el-icon>
          <template #title>
            <span>{{ child.meta?.title }}</span>
          </template>
        </el-menu-item>
      </template>
    </el-sub-menu>
    
    <!-- 单个菜单项 -->
    <el-menu-item
      v-else
      :index="resolvePath"
    >
      <el-icon v-if="route.meta?.icon">
        <component :is="route.meta.icon" />
      </el-icon>
      <template #title>
        <span>{{ route.meta?.title }}</span>
      </template>
    </el-menu-item>
  </template>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { RouteRecordNormalized } from 'vue-router'
import { useUserStore } from '@/stores/user'

interface Props {
  route: RouteRecordNormalized
  basePath?: string
}

const props = withDefaults(defineProps<Props>(), {
  basePath: ''
})

const userStore = useUserStore()

// 可见的子路由
const visibleChildren = computed(() => {
  if (!props.route.children) return []
  
  return props.route.children.filter(child => {
    // 过滤隐藏的路由
    if (child.meta?.hidden) return false
    
    // 检查权限
    if (child.meta?.roles && child.meta.roles.length > 0) {
      return child.meta.roles.includes(userStore.userInfo?.role || '')
    }
    
    return true
  })
})

// 是否有子菜单
const hasChildren = computed(() => {
  return visibleChildren.value.length > 0
})

// 解析路径
const resolvePath = computed(() => {
  if (hasChildren.value) {
    // 如果有子菜单，返回第一个子菜单的完整路径
    const firstChild = visibleChildren.value[0]
    if (firstChild) {
      // 拼接父路由和子路由路径
      const parentPath = props.route.path
      const childPath = firstChild.path
      // 如果子路径是相对路径，需要拼接父路径
      if (childPath.startsWith('/')) {
        return childPath
      } else {
        return `${parentPath}/${childPath}`
      }
    }
    return props.route.path
  }

  return props.route.path
})

// 获取子路由的完整路径
const getChildPath = (child: any) => {
  const parentPath = props.route.path
  const childPath = child.path

  // 如果子路径是绝对路径，直接返回
  if (childPath.startsWith('/')) {
    console.log(`子路由绝对路径: ${childPath}`)
    return childPath
  }

  // 如果是相对路径，拼接父路径
  const fullPath = `${parentPath}/${childPath}`
  console.log(`子路由相对路径: ${childPath}, 完整路径: ${fullPath}`)
  return fullPath
}
</script>
